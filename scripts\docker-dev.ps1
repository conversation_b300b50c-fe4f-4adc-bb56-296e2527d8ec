# PowerShell script for Docker development environment management

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("up", "down", "build", "logs", "restart", "clean", "status")]
    [string]$Action
)

$ErrorActionPreference = "Stop"

function Write-Info {
    param([string]$Message)
    Write-Host "🐳 $Message" -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

# Check if Docker is running
try {
    docker info | Out-Null
} catch {
    Write-Error "Docker is not running. Please start Docker Desktop."
    exit 1
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    if (Test-Path ".env.example") {
        Write-Info "Creating .env file from .env.example"
        Copy-Item ".env.example" ".env"
        Write-Info "Please update .env file with your actual configuration values"
    } else {
        Write-Error ".env file not found. Please create one based on .env.example"
        exit 1
    }
}

switch ($Action) {
    "up" {
        Write-Info "Starting development environment..."
        docker-compose -f docker-compose.dev.yml up -d
        Write-Success "Development environment started!"
        Write-Info "Frontend: http://localhost:3001"
        Write-Info "Backend: http://localhost:5001"
        Write-Info "MongoDB: mongodb://localhost:27017"
    }
    
    "down" {
        Write-Info "Stopping development environment..."
        docker-compose -f docker-compose.dev.yml down
        Write-Success "Development environment stopped!"
    }
    
    "build" {
        Write-Info "Building development images..."
        docker-compose -f docker-compose.dev.yml build --no-cache
        Write-Success "Development images built!"
    }
    
    "logs" {
        Write-Info "Showing logs..."
        docker-compose -f docker-compose.dev.yml logs -f
    }
    
    "restart" {
        Write-Info "Restarting development environment..."
        docker-compose -f docker-compose.dev.yml restart
        Write-Success "Development environment restarted!"
    }
    
    "clean" {
        Write-Info "Cleaning up development environment..."
        docker-compose -f docker-compose.dev.yml down -v
        docker system prune -f
        Write-Success "Development environment cleaned!"
    }

    "status" {
        Write-Info "Checking development environment status..."
        docker-compose -f docker-compose.dev.yml ps
        Write-Info "Running health check..."
        & "$PSScriptRoot\docker-health-check.ps1" dev
    }
}
