# AI Blog Platform - Production Ready

A comprehensive AI-powered blog generation platform that creates SEO-optimized, company-specific content with automated WordPress deployment capabilities.

## 🚀 Features

- **AI-Powered Content Generation**: Google Vertex AI with Gemini fallback
- **SEO Optimization**: Built-in RankMath compliance (85-88/100 scores)
- **Dynamic Image Generation**: AI-generated images with company branding
- **WordPress Integration**: One-click deployment to WordPress sites
- **Company-Specific Content**: Tailored content based on company profiles
- **Real-time Preview**: Live editing and preview capabilities

## 🏗️ Architecture

```
📦 AI Blog Platform
├── 🎨 Frontend (Next.js) - Port 3001
├── 🔧 Backend (Node.js/Express) - Port 5001
└── 🗄️ Database (MongoDB) - Port 27017
```

## 🚀 Quick Start

### Prerequisites
- Docker Desktop installed and running
- Git for cloning the repository

### 1. Clone & Setup
```bash
git clone <repository-url>
cd blog_gen
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example ai-blog-platform-backend/.env

# Edit with your API keys and configuration
# Required: GEMINI_API_KEY, AWS credentials, MongoDB settings
```

### 3. Production Deployment
```powershell
# Deploy entire stack
.\scripts\docker-prod.ps1 deploy

# Check status
.\scripts\docker-prod.ps1 status
```

### 4. Development Environment
```powershell
# Start development with hot reloading
.\scripts\docker-dev.ps1 up
```

## 📁 Project Structure

```
blog_gen/
├── ai-blog-platform-backend/          # Node.js API Server
│   ├── controllers/                   # Route controllers
│   ├── middleware/                    # Express middleware
│   ├── models/                        # MongoDB models
│   ├── routes/                        # API routes
│   ├── services/                      # Business logic
│   ├── scripts/                       # Utility scripts
│   ├── Dockerfile                     # Production container
│   ├── Dockerfile.dev                 # Development container
│   ├── package.json                   # Dependencies
│   └── .env                          # Environment variables
│
├── ai-blog-platform-frontend/         # Next.js Frontend
│   ├── app/                           # Next.js app directory
│   ├── components/                    # React components
│   ├── hooks/                         # Custom React hooks
│   ├── lib/                          # Utility libraries
│   ├── styles/                       # CSS/Tailwind styles
│   ├── types/                        # TypeScript types
│   ├── utils/                        # Helper functions
│   ├── Dockerfile                    # Production container
│   ├── Dockerfile.dev                # Development container
│   └── package.json                  # Dependencies
│
├── scripts/                          # Management scripts
│   ├── docker-prod.ps1              # Production deployment
│   └── docker-dev.ps1               # Development environment
│
├── docker-compose.prod.yml           # Production configuration
├── docker-compose.dev.yml            # Development configuration
├── .env.example                      # Environment template
└── README.md                         # This file
```

## 🔧 Available Commands

### Production Management
```powershell
# Deploy entire stack
.\scripts\docker-prod.ps1 deploy

# Start services
.\scripts\docker-prod.ps1 up

# Stop services
.\scripts\docker-prod.ps1 down

# View logs
.\scripts\docker-prod.ps1 logs

# Check status
.\scripts\docker-prod.ps1 status

# Clean environment
.\scripts\docker-prod.ps1 clean
```

### Development Management
```powershell
# Start development environment
.\scripts\docker-dev.ps1 up

# Stop development environment
.\scripts\docker-dev.ps1 down

# View logs
.\scripts\docker-dev.ps1 logs

# Rebuild images
.\scripts\docker-dev.ps1 build
```

## 🌐 Service URLs

After deployment, access the services at:
- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:5001
- **MongoDB**: mongodb://localhost:27017

## 📝 Environment Configuration

Required environment variables in `ai-blog-platform-backend/.env`:

```env
# API Keys
GEMINI_API_KEY=your_gemini_api_key
PERPLEXITY_API_KEY=your_perplexity_api_key
SERP_API_KEY=your_serp_api_key

# Google Cloud
GOOGLE_APPLICATION_CREDENTIALS=./service_account_key.json
GOOGLE_PROJECT_ID=your_project_id

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BUCKET=your_s3_bucket

# WordPress
WORDPRESS_API_URL=your_wordpress_url
WORDPRESS_USERNAME=your_username
WORDPRESS_PASSWORD=your_password

# Database
MONGODB_URI=mongodb://mongodb:27017/blog_platform
```

## 🐳 Docker Optimization

The Docker setup is optimized for production:
- **Backend**: ~60MB (Alpine Linux + Node.js + production deps only)
- **Frontend**: ~80MB (Multi-stage build with Next.js standalone)
- **Total**: ~140MB (90% smaller than typical setups)

## 🔒 Security Features

- Non-root users in all containers
- Health checks for all services
- Resource limits to prevent runaway containers
- Environment variable isolation
- Minimal attack surface with Alpine Linux

## 🚀 Deployment

### Local Development
1. Clone repository
2. Copy `.env.example` to `ai-blog-platform-backend/.env`
3. Configure environment variables
4. Run `.\scripts\docker-dev.ps1 up`

### Production Deployment
1. Ensure all environment variables are configured
2. Run `.\scripts\docker-prod.ps1 deploy`
3. Monitor with `.\scripts\docker-prod.ps1 status`

## 📊 Monitoring

Check system health:
```powershell
# Service status
.\scripts\docker-prod.ps1 status

# Resource usage
docker stats

# Container logs
.\scripts\docker-prod.ps1 logs
```

## 🛠️ Troubleshooting

### Common Issues
1. **Port conflicts**: Ensure ports 3001, 5001, 27017 are available
2. **Environment variables**: Verify all required variables are set
3. **Docker resources**: Ensure Docker has sufficient memory allocated
4. **Service account**: Verify Google Cloud service account key exists

### Debug Commands
```powershell
# Check container status
docker ps

# View specific service logs
docker logs blog-platform-backend

# Access container shell
docker exec -it blog-platform-backend sh
```

## 📄 License

MIT License - See LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Production Ready** ✅ | **Docker Optimized** ✅ | **Scalable Architecture** ✅
