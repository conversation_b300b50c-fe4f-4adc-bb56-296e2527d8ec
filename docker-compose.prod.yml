version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: blog-platform-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password123}
      MONGO_INITDB_DATABASE: ${MONGO_DB_NAME:-blog_platform}
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - blog-platform
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  backend:
    build:
      context: ./ai-blog-platform-backend
      dockerfile: Dockerfile
      args:
        NODE_ENV: production
    image: blog-platform-backend:latest
    container_name: blog-platform-backend
    ports:
      - "5001:5001"
    environment:
      - NODE_ENV=production
      - PORT=5001
      - MONGODB_URI=mongodb://mongodb:27017/${MONGO_DB_NAME:-blog_platform}
    env_file:
      - ./ai-blog-platform-backend/.env
    volumes:
      - backend_uploads:/app/uploads
    depends_on:
      mongodb:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:5001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 15s
    networks:
      - blog-platform

  frontend:
    build:
      context: ./ai-blog-platform-frontend
      dockerfile: Dockerfile
      args:
        NODE_ENV: production
    image: blog-platform-frontend:latest
    container_name: blog-platform-frontend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://backend:5001/api
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.3'
        reservations:
          memory: 128M
          cpus: '0.15'
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 15s
    networks:
      - blog-platform

volumes:
  backend_uploads:
    driver: local
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local

networks:
  blog-platform:
    driver: bridge
