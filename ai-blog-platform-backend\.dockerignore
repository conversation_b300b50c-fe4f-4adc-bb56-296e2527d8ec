# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Development files
.git
.gitignore
.vscode
.idea
*.swp
*.swo

# Documentation
README.md
*.md
docs/

# Environment files
.env*
!.env.production

# Logs
logs/
*.log

# Test files
test/
tests/
__tests__/
*.test.js
*.spec.js
coverage/
.nyc_output

# Build artifacts
dist/
build/
.next/

# OS files
.DS_Store
Thumbs.db
.Spotlight-V100
.Trashes

# Temporary files
tmp/
temp/
*.tmp
*.bak
*.backup

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Scripts and configs not needed in container
config/
*.config.js
!next.config.*

# Keep uploads directory structure but exclude content
uploads/*
!uploads/.gitkeep

