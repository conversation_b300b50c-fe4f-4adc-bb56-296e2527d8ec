# syntax=docker/dockerfile:1

# Development stage
FROM node:18-alpine AS development

# Install development dependencies
RUN apk add --no-cache dumb-init

# Create app user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodeuser -u 1001

WORKDIR /app

# Copy package files
COPY --chown=nodeuser:nodejs package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci && \
    npm cache clean --force

# Create required directories
RUN mkdir -p uploads logs && \
    chown nodeuser:nodejs uploads logs

# Copy service account key if exists
COPY --chown=nodeuser:nodejs service_account_key.json* ./

# Set permissions for service account key if exists
RUN if [ -f service_account_key.json ]; then \
      chmod 600 service_account_key.json; \
    fi

EXPOSE 5001 9229
USER nodeuser

# Default command for development
CMD ["npm", "run", "dev"]
