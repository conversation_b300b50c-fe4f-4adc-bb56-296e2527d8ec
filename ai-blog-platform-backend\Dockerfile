# syntax=docker/dockerfile:1

# Production optimized backend
FROM node:18-alpine AS production

# Install minimal runtime dependencies in single layer
RUN apk add --no-cache dumb-init && \
    addgroup -g 1001 -S nodejs && \
    adduser -S nodeuser -u 1001 && \
    rm -rf /var/cache/apk/*

WORKDIR /app

# Copy package files first (better layer caching)
COPY --chown=nodeuser:nodejs package*.json ./

# Install ONLY production dependencies and clean in single layer
RUN npm ci --only=production --omit=dev --no-audit --no-fund && \
    npm cache clean --force && \
    rm -rf ~/.npm /tmp/* /var/tmp/* /usr/local/lib/node_modules/npm/man /usr/local/lib/node_modules/npm/doc

# Copy application code in optimal order
COPY --chown=nodeuser:nodejs server.js check-env.js ./
COPY --chown=nodeuser:nodejs controllers/ ./controllers/
COPY --chown=nodeuser:nodejs middleware/ ./middleware/
COPY --chown=nodeuser:nodejs models/ ./models/
COPY --chown=nodeuser:nodejs routes/ ./routes/
COPY --chown=nodeuser:nodejs services/ ./services/
COPY --chown=nodeuser:nodejs scripts/ ./scripts/

# Handle service account key (optional file)
COPY --chown=nodeuser:nodejs service_account_key.json* ./

# Create directories and set permissions in single layer
RUN mkdir -p uploads logs && \
    chown -R nodeuser:nodejs uploads logs && \
    if [ -f service_account_key.json ]; then chmod 600 service_account_key.json; fi

# Switch to non-root user
USER nodeuser

# Health check with timeout
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
  CMD node -e "require('http').get('http://localhost:5001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

EXPOSE 5001

ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]

