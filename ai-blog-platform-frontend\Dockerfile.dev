# syntax=docker/dockerfile:1

# Development stage
FROM node:18-alpine AS development

# Install development dependencies
RUN apk add --no-cache libc6-compat

# Create app user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /app

# Copy package files
COPY --chown=nextjs:nodejs package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci --legacy-peer-deps && \
    npm cache clean --force

# Set environment for development
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3001

EXPOSE 3001
USER nextjs

# Default command for development
CMD ["npm", "run", "dev"]
