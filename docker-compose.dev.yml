version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: blog-platform-mongodb-dev
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password123}
      MONGO_INITDB_DATABASE: ${MONGO_DB_NAME:-blog_platform_dev}
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data_dev:/data/db
      - mongodb_config_dev:/data/configdb
    networks:
      - blog-platform-dev
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  backend:
    build:
      context: ./ai-blog-platform-backend
      dockerfile: Dockerfile.dev
      target: development
    container_name: blog-platform-backend-dev
    ports:
      - "5001:5001"
      - "9229:9229"  # Debug port
    environment:
      - NODE_ENV=development
      - PORT=5001
      - MONGODB_URI=mongodb://mongodb:27017/${MONGO_DB_NAME:-blog_platform_dev}
    env_file:
      - ./ai-blog-platform-backend/.env
    volumes:
      - ./ai-blog-platform-backend:/app
      - /app/node_modules
      - backend_uploads_dev:/app/uploads
    depends_on:
      mongodb:
        condition: service_healthy
    restart: unless-stopped
    command: npm run dev
    networks:
      - blog-platform-dev

  frontend:
    build:
      context: ./ai-blog-platform-frontend
      dockerfile: Dockerfile.dev
      target: development
    container_name: blog-platform-frontend-dev
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:5001/api
      - WATCHPACK_POLLING=true
    volumes:
      - ./ai-blog-platform-frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    restart: unless-stopped
    command: npm run dev
    networks:
      - blog-platform-dev

volumes:
  backend_uploads_dev:
    driver: local
  mongodb_data_dev:
    driver: local
  mongodb_config_dev:
    driver: local

networks:
  blog-platform-dev:
    driver: bridge
